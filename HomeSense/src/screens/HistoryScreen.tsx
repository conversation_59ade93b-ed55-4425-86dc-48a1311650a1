import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Colors, Typography, Spacing, Layout } from '../constants/theme';

interface MotionEvent {
  id: string;
  time: string;
  details?: string;
  expanded?: boolean;
}

interface DayGroup {
  date: string;
  events: MotionEvent[];
}

const HistoryScreen: React.FC = () => {
  const navigation = useNavigation();
  const [expandedEvents, setExpandedEvents] = useState<Set<string>>(new Set());

  // Mock data matching the reference design
  const historyData: DayGroup[] = [
    {
      date: 'Today',
      events: [
        { id: '1', time: '14:30', details: '' },
        { id: '2', time: '12:15', details: '' },
      ]
    },
    {
      date: 'Yesterday',
      events: [
        {
          id: '3',
          time: '18:45',
          details: 'Motion has been detected in your home.\nIf this was not expected you might',
          expanded: true
        },
      ]
    },
    {
      date: 'Wednesday - 12/12/2025',
      events: [
        { id: '4', time: '09:20', details: '' },
        { id: '5', time: '16:45', details: '' },
        { id: '6', time: '11:30', details: '' },
        { id: '7', time: '08:15', details: '' },
        { id: '8', time: '19:22', details: '' },
      ]
    }
  ];

  const toggleExpanded = (eventId: string) => {
    const newExpanded = new Set(expandedEvents);
    if (newExpanded.has(eventId)) {
      newExpanded.delete(eventId);
    } else {
      newExpanded.add(eventId);
    }
    setExpandedEvents(newExpanded);
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const renderMotionEvent = (event: MotionEvent, isLast: boolean) => {
    const isExpanded = expandedEvents.has(event.id) || event.expanded;

    return (
      <View key={event.id} style={styles.eventContainer}>
        {!isLast && <View style={styles.timelineLine} />}

        <TouchableOpacity
          style={styles.eventCard}
          onPress={() => toggleExpanded(event.id)}
          activeOpacity={0.7}
        >
          <View style={styles.eventHeader}>
            <Text style={styles.eventTitle}>Motion has been detected</Text>
            <Ionicons
              name={isExpanded ? "chevron-up" : "chevron-down"}
              size={20}
              color={Colors.gray600}
            />
          </View>

          {isExpanded && event.details && (
            <View style={styles.eventDetails}>
              <Text style={styles.eventDetailsText}>{event.details}</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>
    );
  };

  const renderDayGroup = (dayGroup: DayGroup) => (
    <View key={dayGroup.date} style={styles.dayGroup}>
      <View style={styles.dayHeader}>
        <View style={styles.dayDot} />
        <Text style={styles.dayTitle}>{dayGroup.date}</Text>
      </View>

      <View style={styles.eventsContainer}>
        {dayGroup.events.map((event, index) =>
          renderMotionEvent(event, index === dayGroup.events.length - 1)
        )}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color={Colors.black} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Detection history</Text>
        <View style={styles.headerSpacer} />
      </View>

      {/* Timeline Content */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.timeline}>
          {historyData.map(renderDayGroup)}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Layout.screenPadding,
    paddingVertical: Spacing.md,
    backgroundColor: Colors.white,
  },
  backButton: {
    padding: Spacing.xs,
    marginRight: Spacing.sm,
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.black,
    fontFamily: Typography.fontFamily.primary,
    flex: 1,
    textAlign: 'center',
    marginRight: 40, // Compensate for back button
  },
  headerSpacer: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: Layout.screenPadding,
    paddingBottom: Spacing.xl,
  },
  timeline: {
    paddingTop: Spacing.md,
  },
  dayGroup: {
    marginBottom: Spacing.xl,
  },
  dayHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  dayDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.black,
    marginRight: Spacing.sm,
  },
  dayTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.black,
    fontFamily: Typography.fontFamily.primary,
  },
  eventsContainer: {
    marginLeft: 6, // Center align with dot
  },
  eventContainer: {
    position: 'relative',
    marginBottom: Spacing.sm,
  },
  timelineLine: {
    position: 'absolute',
    left: -6,
    top: 0,
    bottom: -Spacing.sm,
    width: 1,
    backgroundColor: Colors.gray300,
  },
  eventCard: {
    backgroundColor: Colors.gray100,
    borderRadius: 12,
    padding: Spacing.md,
    marginLeft: Spacing.md,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  eventTitle: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.black,
    fontFamily: Typography.fontFamily.primary,
    flex: 1,
  },
  eventDetails: {
    marginTop: Spacing.sm,
    paddingTop: Spacing.sm,
  },
  eventDetailsText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.black,
    fontFamily: Typography.fontFamily.primary,
    lineHeight: 20,
  },
});

export default HistoryScreen;
