import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { <PERSON><PERSON>, <PERSON> } from '../components';
import { Colors, Typography, Spacing, Layout } from '../constants/theme';
import { HistoryEntry } from '../types';
import { mockHistoryEntries } from '../utils/mockData';

const HistoryScreen: React.FC = () => {
  const [historyData, setHistoryData] = useState<HistoryEntry[]>(mockHistoryEntries);
  const [filter, setFilter] = useState<'all' | 'today' | 'week'>('all');

  const getFilteredData = () => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    switch (filter) {
      case 'today':
        return historyData.filter(item => item.timestamp >= today);
      case 'week':
        return historyData.filter(item => item.timestamp >= weekAgo);
      default:
        return historyData;
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);

    if (date >= today) return 'Today';
    if (date >= yesterday) return 'Yesterday';
    return date.toLocaleDateString();
  };

  const getMotionIcon = (type: string) => {
    switch (type) {
      case 'person': return 'person';
      case 'pet': return 'paw';
      case 'object': return 'cube';
      default: return 'help-circle';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return Colors.success;
    if (confidence >= 60) return Colors.warning;
    return Colors.error;
  };

  const handleExport = () => {
    Alert.alert(
      'Export History',
      'Export motion detection history as CSV or PDF?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'CSV', onPress: () => Alert.alert('Exported', 'History exported as CSV') },
        { text: 'PDF', onPress: () => Alert.alert('Exported', 'History exported as PDF') },
      ]
    );
  };

  const renderHistoryItem = ({ item }: { item: HistoryEntry }) => (
    <Card style={styles.historyItem}>
      <View style={styles.itemHeader}>
        <View style={styles.itemInfo}>
          <Ionicons 
            name={getMotionIcon(item.type)} 
            size={24} 
            color={Colors.black} 
          />
          <View style={styles.itemDetails}>
            <Text style={styles.itemRoom}>{item.room}</Text>
            <Text style={styles.itemTime}>
              {formatDate(item.timestamp)} at {formatTime(item.timestamp)}
            </Text>
          </View>
        </View>
        <View style={styles.itemMeta}>
          <Text style={[styles.confidence, { color: getConfidenceColor(item.confidence) }]}>
            {item.confidence}%
          </Text>
          {item.isBookmarked && (
            <Ionicons name="bookmark" size={16} color={Colors.accent} />
          )}
        </View>
      </View>
      
      <View style={styles.itemFooter}>
        <Text style={styles.duration}>Duration: {item.duration}s</Text>
        <Text style={styles.deviceName}>{item.deviceName}</Text>
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Filter Buttons */}
      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'all' && styles.filterButtonActive]}
          onPress={() => setFilter('all')}
        >
          <Text style={[styles.filterText, filter === 'all' && styles.filterTextActive]}>
            All
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.filterButton, filter === 'today' && styles.filterButtonActive]}
          onPress={() => setFilter('today')}
        >
          <Text style={[styles.filterText, filter === 'today' && styles.filterTextActive]}>
            Today
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.filterButton, filter === 'week' && styles.filterButtonActive]}
          onPress={() => setFilter('week')}
        >
          <Text style={[styles.filterText, filter === 'week' && styles.filterTextActive]}>
            This Week
          </Text>
        </TouchableOpacity>
      </View>

      {/* Export Button */}
      <View style={styles.exportContainer}>
        <Button
          title="Export History"
          onPress={handleExport}
          variant="outline"
          size="sm"
          icon="download-outline"
        />
      </View>

      {/* History List */}
      <FlatList
        data={getFilteredData()}
        renderItem={renderHistoryItem}
        keyExtractor={(item) => item.id}
        style={styles.list}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.primary,
  },
  filterContainer: {
    flexDirection: 'row',
    padding: Layout.screenPadding,
    gap: Spacing.sm,
  },
  filterButton: {
    flex: 1,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: 20,
    backgroundColor: Colors.white,
    alignItems: 'center',
  },
  filterButtonActive: {
    backgroundColor: Colors.black,
  },
  filterText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.black,
    fontFamily: Typography.fontFamily.primary,
  },
  filterTextActive: {
    color: Colors.white,
  },
  exportContainer: {
    paddingHorizontal: Layout.screenPadding,
    paddingBottom: Spacing.md,
    alignItems: 'flex-end',
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: Layout.screenPadding,
    paddingTop: 0,
    paddingBottom: Layout.tabBarHeight,
  },
  historyItem: {
    marginBottom: Spacing.md,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.sm,
  },
  itemInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  itemDetails: {
    marginLeft: Spacing.md,
    flex: 1,
  },
  itemRoom: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.black,
    fontFamily: Typography.fontFamily.primary,
  },
  itemTime: {
    fontSize: Typography.fontSize.sm,
    color: Colors.gray600,
    fontFamily: Typography.fontFamily.primary,
    marginTop: 2,
  },
  itemMeta: {
    alignItems: 'flex-end',
    gap: Spacing.xs,
  },
  confidence: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.bold,
    fontFamily: Typography.fontFamily.primary,
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  duration: {
    fontSize: Typography.fontSize.sm,
    color: Colors.gray600,
    fontFamily: Typography.fontFamily.primary,
  },
  deviceName: {
    fontSize: Typography.fontSize.sm,
    color: Colors.gray600,
    fontFamily: Typography.fontFamily.primary,
  },
});

export default HistoryScreen;
