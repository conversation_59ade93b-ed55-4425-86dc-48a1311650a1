import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Colors, Typography, Spacing, Layout } from '../constants/theme';

interface MotionEvent {
  id: string;
  time: string;
  details?: string;
  expanded?: boolean;
}

interface DayGroup {
  date: string;
  events: MotionEvent[];
}

const HistoryScreen: React.FC = () => {
  const navigation = useNavigation();
  const [expandedEvents, setExpandedEvents] = useState<Set<string>>(new Set());

  // Enhanced mock data with more realistic details
  const historyData: DayGroup[] = [
    {
      date: 'Today',
      events: [
        {
          id: '1',
          time: '14:30',
          details: 'Motion detected in living room area. Duration: 2 minutes. Confidence: 95%'
        },
        {
          id: '2',
          time: '12:15',
          details: 'Motion detected near front entrance. Duration: 30 seconds. Confidence: 87%'
        },
      ]
    },
    {
      date: 'Yesterday',
      events: [
        {
          id: '3',
          time: '18:45',
          details: 'Motion has been detected in your home.\nIf this was not expected you might want to check your security settings or contact support.',
        },
        {
          id: '4',
          time: '15:20',
          details: 'Motion detected in kitchen area. Duration: 5 minutes. Confidence: 92%'
        },
      ]
    },
    {
      date: 'Wednesday - 12/12/2025',
      events: [
        { id: '5', time: '09:20', details: 'Motion detected in bedroom. Duration: 1 minute. Confidence: 78%' },
        { id: '6', time: '16:45', details: 'Motion detected near back door. Duration: 45 seconds. Confidence: 89%' },
        { id: '7', time: '11:30', details: 'Motion detected in hallway. Duration: 20 seconds. Confidence: 94%' },
        { id: '8', time: '08:15', details: 'Motion detected in living room. Duration: 3 minutes. Confidence: 91%' },
        { id: '9', time: '19:22', details: 'Motion detected in garage area. Duration: 1.5 minutes. Confidence: 85%' },
      ]
    }
  ];

  const toggleExpanded = (eventId: string) => {
    const newExpanded = new Set(expandedEvents);
    if (newExpanded.has(eventId)) {
      newExpanded.delete(eventId);
    } else {
      newExpanded.add(eventId);
    }
    setExpandedEvents(newExpanded);
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const renderMotionEvent = (event: MotionEvent, isLast: boolean, isFirst: boolean) => {
    const isExpanded = expandedEvents.has(event.id);

    return (
      <View key={event.id} style={styles.eventContainer}>
        {/* Event card with left border accent */}
        <TouchableOpacity
          style={[
            styles.eventCard,
            isExpanded && styles.eventCardExpanded
          ]}
          onPress={() => toggleExpanded(event.id)}
          activeOpacity={0.8}
        >
          <View style={styles.eventHeader}>
            <View style={styles.eventTitleContainer}>
              <Text style={styles.eventTitle}>Motion has been detected</Text>
              <View style={styles.eventMeta}>
                <Text style={styles.eventTime}>{event.time}</Text>
                <View style={styles.statusDot} />
              </View>
            </View>
            <Ionicons
              name={isExpanded ? "chevron-up" : "chevron-down"}
              size={24}
              color={Colors.gray600}
            />
          </View>

          {isExpanded && (
            <Animated.View style={styles.eventDetails}>
              <View style={styles.detailsSeparator} />
              <Text style={styles.eventDetailsText}>{event.details}</Text>
            </Animated.View>
          )}
        </TouchableOpacity>
      </View>
    );
  };

  const renderDayGroup = (dayGroup: DayGroup, groupIndex: number) => (
    <View key={dayGroup.date} style={styles.dayGroup}>
      {/* Day header with modern design */}
      <View style={styles.dayHeader}>
        <View style={styles.dayIndicator}>
          <Text style={styles.dayIndicatorText}>{dayGroup.events.length}</Text>
        </View>
        <Text style={styles.dayTitle}>{dayGroup.date}</Text>
      </View>

      {/* Events container with clean spacing */}
      <View style={styles.eventsContainer}>
        {dayGroup.events.map((event, index) =>
          renderMotionEvent(
            event,
            index === dayGroup.events.length - 1,
            index === 0
          )
        )}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color={Colors.black} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Detection history</Text>
        <View style={styles.headerSpacer} />
      </View>

      {/* Timeline Content */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.timeline}>
          {historyData.map((dayGroup, index) => renderDayGroup(dayGroup, index))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Layout.screenPadding,
    paddingVertical: Spacing.md,
    backgroundColor: Colors.white,
  },
  backButton: {
    padding: Spacing.xs,
    marginRight: Spacing.sm,
  },
  headerTitle: {
    fontSize: 12,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.black,
    fontFamily: Typography.fontFamily.primary,
    flex: 1,
    textAlign: 'center',
    marginRight: 40, // Compensate for back button
  },
  headerSpacer: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: Layout.screenPadding,
    paddingBottom: Spacing.xl,
  },
  timeline: {
    paddingTop: Spacing.md,
  },
  dayGroup: {
    marginBottom: Spacing.xl * 1.5,
  },
  dayHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.lg,
    paddingHorizontal: Spacing.sm,
  },
  dayIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.black,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  dayIndicatorText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.white,
    fontFamily: Typography.fontFamily.primary,
  },
  dayTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.black,
    fontFamily: Typography.fontFamily.primary,
  },
  eventsContainer: {
    gap: Spacing.md,
  },
  eventContainer: {
    // Clean container without complex positioning
  },
  eventCard: {
    backgroundColor: Colors.gray100,
    borderRadius: 16,
    padding: Spacing.lg,
    marginHorizontal: Spacing.sm,
    shadowColor: Colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  eventCardExpanded: {
    backgroundColor: Colors.gray200,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  eventTitleContainer: {
    flex: 1,
    marginRight: Spacing.sm,
  },
  eventTitle: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.black,
    fontFamily: Typography.fontFamily.primary,
    marginBottom: Spacing.xs,
  },
  eventMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  eventTime: {
    fontSize: Typography.fontSize.sm,
    color: Colors.gray600,
    fontFamily: Typography.fontFamily.primary,
    fontWeight: Typography.fontWeight.medium,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.success,
  },
  eventDetails: {
    overflow: 'hidden',
  },
  detailsSeparator: {
    height: 1,
    backgroundColor: Colors.gray300,
    marginVertical: Spacing.sm,
  },
  eventDetailsText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.gray700,
    fontFamily: Typography.fontFamily.primary,
    lineHeight: 20,
  },
});

export default HistoryScreen;
