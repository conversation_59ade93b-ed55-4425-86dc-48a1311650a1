import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Dimensions,
  Animated,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Colors, Typography, Spacing, Layout } from '../constants/theme';

const { width } = Dimensions.get('window');

const HomeScreen: React.FC = () => {
  const navigation = useNavigation();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [motionDetected, setMotionDetected] = useState(true);
  const [movementCount] = useState(3);
  const [timeRange] = useState('16:05h and 16:15h');
  const [showToast, setShowToast] = useState(false);

  // Animation values
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;
  const toastAnim = useRef(new Animated.Value(0)).current;

  // Motion detection breathing animation
  useEffect(() => {
    if (motionDetected) {
      const breathingAnimation = Animated.loop(
        Animated.sequence([
          Animated.parallel([
            Animated.timing(scaleAnim, {
              toValue: 1.1,
              duration: 1000,
              useNativeDriver: true,
            }),
            Animated.timing(opacityAnim, {
              toValue: 0.7,
              duration: 1000,
              useNativeDriver: true,
            }),
          ]),
          Animated.parallel([
            Animated.timing(scaleAnim, {
              toValue: 1,
              duration: 1000,
              useNativeDriver: true,
            }),
            Animated.timing(opacityAnim, {
              toValue: 1,
              duration: 1000,
              useNativeDriver: true,
            }),
          ]),
        ])
      );
      breathingAnimation.start();
      return () => breathingAnimation.stop();
    }
  }, [motionDetected]);

  // Toast animation
  useEffect(() => {
    if (showToast) {
      Animated.sequence([
        Animated.timing(toastAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.delay(2000),
        Animated.timing(toastAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => setShowToast(false));
    }
  }, [showToast]);

  const handleNotificationToggle = (value: boolean) => {
    setNotificationsEnabled(value);
    if (value) {
      setShowToast(true);
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const handleDetectionHistory = () => {
    navigation.navigate('History' as never);
  };

  const handleAdaptSettings = () => {
    navigation.navigate('Sensitivity' as never);
  };

  const handleSupportFAQ = () => {
    navigation.navigate('Help' as never);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="chevron-back" size={24} color={Colors.black} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Overview</Text>
          <View style={styles.headerSpacer} />
        </View>

        {/* Motion Detection Circle */}
        <View style={styles.motionContainer}>
          <Animated.View
            style={[
              styles.motionCircle,
              {
                transform: [{ scale: scaleAnim }],
                opacity: opacityAnim,
              }
            ]}
          >
            <Text style={styles.motionText}>Motion</Text>
            <Text style={styles.motionText}>Detected!</Text>
          </Animated.View>
        </View>

        {/* Movement Summary Card */}
        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>
            {movementCount} Movements in the last hour
          </Text>
          <Text style={styles.summarySubtitle}>
            Just now between {timeRange}
          </Text>

          <TouchableOpacity style={styles.historyButton} onPress={handleDetectionHistory}>
            <Text style={styles.historyButtonText}>Detection history</Text>
          </TouchableOpacity>
        </View>

        {/* Menu Items */}
        <View style={styles.menuContainer}>
          {/* Get notified */}
          <View style={[styles.menuItem, styles.menuItemFirst]}>
            <Text style={styles.menuItemText}>Get notified</Text>
            <Switch
              value={notificationsEnabled}
              onValueChange={handleNotificationToggle}
              trackColor={{ false: Colors.gray300, true: Colors.black }}
              thumbColor={Colors.white}
            />
          </View>

          {/* Divider */}
          <View style={styles.menuDivider} />

          {/* Adapt settings */}
          <TouchableOpacity style={styles.menuItem} onPress={handleAdaptSettings}>
            <Text style={styles.menuItemText}>Adapt settings</Text>
            <Ionicons name="chevron-forward" size={20} color={Colors.black} />
          </TouchableOpacity>

          {/* Divider */}
          <View style={styles.menuDivider} />

          {/* Support and FAQ */}
          <TouchableOpacity style={[styles.menuItem, styles.menuItemLast]} onPress={handleSupportFAQ}>
            <Text style={styles.menuItemText}>Support and FAQ</Text>
            <Ionicons name="chevron-forward" size={20} color={Colors.black} />
          </TouchableOpacity>
        </View>

        {/* Toast Notification */}
        {showToast && (
          <Animated.View
            style={[
              styles.toast,
              {
                opacity: toastAnim,
                transform: [
                  {
                    translateY: toastAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [50, 0],
                    }),
                  },
                ],
              }
            ]}
          >
            <Text style={styles.toastText}>Notifications enabled</Text>
          </Animated.View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.primary, // DT Yellow background
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    marginTop: 8,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.black,
    fontFamily: Typography.fontFamily.primary,
  },
  headerSpacer: {
    width: 40,
  },
  motionContainer: {
    alignItems: 'center',
    marginVertical: 40,
  },
  motionCircle: {
    width: width * 0.6,
    height: width * 0.6,
    borderRadius: (width * 0.6) / 2,
    backgroundColor: Colors.black,
    alignItems: 'center',
    justifyContent: 'center',
  },
  motionText: {
    fontSize: 24,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.white,
    fontFamily: Typography.fontFamily.primary,
    textAlign: 'center',
  },
  summaryCard: {
    backgroundColor: Colors.white,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.black,
    fontFamily: Typography.fontFamily.primary,
    marginBottom: 4,
  },
  summarySubtitle: {
    fontSize: 14,
    color: Colors.gray600,
    fontFamily: Typography.fontFamily.primary,
    marginBottom: 16,
  },
  historyButton: {
    backgroundColor: Colors.gray100,
    borderRadius: 24,
    paddingVertical: 12,
    paddingHorizontal: 20,
    alignSelf: 'flex-start',
  },
  historyButtonText: {
    fontSize: 14,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.black,
    fontFamily: Typography.fontFamily.primary,
  },
  menuContainer: {
    backgroundColor: Colors.white,
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  menuItem: {
    backgroundColor: Colors.white,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  menuItemFirst: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  menuItemLast: {
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  menuDivider: {
    height: 1,
    backgroundColor: Colors.gray200,
    marginHorizontal: 20,
  },
  menuItemText: {
    fontSize: 16,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.black,
    fontFamily: Typography.fontFamily.primary,
  },
  toast: {
    position: 'absolute',
    bottom: 100,
    left: 20,
    right: 20,
    backgroundColor: Colors.black,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
    alignItems: 'center',
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  toastText: {
    fontSize: 16,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.white,
    fontFamily: Typography.fontFamily.primary,
  },
});

export default HomeScreen;
