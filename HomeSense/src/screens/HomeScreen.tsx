import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { <PERSON><PERSON>, Card, MotionCircle } from '../components';
import { Colors, Typography, Spacing, Layout } from '../constants/theme';
import { MotionStatus } from '../types';
import { mockCurrentMotionStatus, updateMockMotionStatus } from '../utils/mockData';

const HomeScreen: React.FC = () => {
  const [motionStatus, setMotionStatus] = useState<MotionStatus>(mockCurrentMotionStatus);
  const [refreshing, setRefreshing] = useState(false);

  // Simulate real-time motion updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Randomly toggle motion status for demo purposes
      const shouldBeActive = Math.random() > 0.7; // 30% chance of motion
      setMotionStatus(updateMockMotionStatus(shouldBeActive));
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setMotionStatus(updateMockMotionStatus(Math.random() > 0.5));
      setRefreshing(false);
    }, 1000);
  };

  const handleViewDetectionZones = () => {
    Alert.alert(
      'Detection Zones',
      'This feature would show a map of your home with motion detection zones highlighted.',
      [{ text: 'OK' }]
    );
  };

  const handleQuickActions = (action: string) => {
    switch (action) {
      case 'arm':
        Alert.alert('Armed', 'Motion detection is now armed for all devices.');
        break;
      case 'disarm':
        Alert.alert('Disarmed', 'Motion detection has been temporarily disabled.');
        break;
      case 'test':
        Alert.alert('Test Mode', 'Testing motion detection on all devices...');
        break;
    }
  };

  const formatLastDetection = (date?: Date) => {
    if (!date) return 'No recent activity';
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    
    return date.toLocaleDateString();
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Main Motion Status Circle */}
        <View style={styles.motionContainer}>
          <MotionCircle
            isActive={motionStatus.isActive}
            confidence={motionStatus.confidence}
            animated={true}
          />
        </View>

        {/* Status Information */}
        <Card style={styles.statusCard}>
          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Last Detection:</Text>
            <Text style={styles.statusValue}>
              {formatLastDetection(motionStatus.lastDetection)}
            </Text>
          </View>
          
          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Active Devices:</Text>
            <Text style={styles.statusValue}>
              {motionStatus.activeDevices.length} of 4
            </Text>
          </View>
          
          {motionStatus.isActive && (
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>Confidence:</Text>
              <Text style={[styles.statusValue, styles.confidenceValue]}>
                {motionStatus.confidence}%
              </Text>
            </View>
          )}
        </Card>

        {/* Quick Actions */}
        <Card style={styles.actionsCard}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <View style={styles.actionButtons}>
            <Button
              title="View Zones"
              onPress={handleViewDetectionZones}
              variant="primary"
              size="md"
              icon="map-outline"
            />
            
            <View style={styles.buttonRow}>
              <Button
                title="Arm All"
                onPress={() => handleQuickActions('arm')}
                variant="secondary"
                size="sm"
                icon="shield-checkmark-outline"
              />
              <Button
                title="Disarm"
                onPress={() => handleQuickActions('disarm')}
                variant="outline"
                size="sm"
                icon="shield-outline"
              />
            </View>
          </View>
        </Card>

        {/* System Status */}
        <Card style={styles.systemCard}>
          <Text style={styles.sectionTitle}>System Status</Text>
          
          <View style={styles.systemStatus}>
            <View style={styles.systemItem}>
              <View style={[styles.statusDot, { backgroundColor: Colors.success }]} />
              <Text style={styles.systemText}>WiFi Connected</Text>
            </View>
            
            <View style={styles.systemItem}>
              <View style={[styles.statusDot, { backgroundColor: Colors.success }]} />
              <Text style={styles.systemText}>Cloud Sync Active</Text>
            </View>
            
            <View style={styles.systemItem}>
              <View style={[styles.statusDot, { backgroundColor: Colors.warning }]} />
              <Text style={styles.systemText}>1 Device Low Battery</Text>
            </View>
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.primary,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: Layout.screenPadding,
    paddingBottom: Layout.tabBarHeight,
  },
  motionContainer: {
    alignItems: 'center',
    marginVertical: Spacing.xl,
  },
  statusCard: {
    marginBottom: Spacing.lg,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  statusLabel: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.gray600,
    fontFamily: Typography.fontFamily.primary,
  },
  statusValue: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.black,
    fontFamily: Typography.fontFamily.primary,
  },
  confidenceValue: {
    color: Colors.success,
  },
  actionsCard: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.black,
    fontFamily: Typography.fontFamily.primary,
    marginBottom: Spacing.md,
  },
  actionButtons: {
    gap: Spacing.md,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: Spacing.sm,
    justifyContent: 'space-between',
  },
  systemCard: {
    marginBottom: Spacing.lg,
  },
  systemStatus: {
    gap: Spacing.sm,
  },
  systemItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: Spacing.sm,
  },
  systemText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.gray700,
    fontFamily: Typography.fontFamily.primary,
  },
});

export default HomeScreen;
