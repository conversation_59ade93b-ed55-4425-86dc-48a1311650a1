import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import { Card } from '../components';
import { Colors, Typography, Spacing, Layout } from '../constants/theme';
import { SensitivityLevel } from '../types';

const SensitivityScreen: React.FC = () => {
  const [sensitivity, setSensitivity] = useState<SensitivityLevel>('medium');
  const [smartMode, setSmartMode] = useState(true);
  const [customValue, setCustomValue] = useState(50);
  const [quietHours, setQuietHours] = useState({
    enabled: true,
    startTime: '22:00',
    endTime: '06:00',
  });

  const sensitivityLevels: { value: SensitivityLevel; label: string; description: string }[] = [
    { 
      value: 'low', 
      label: 'Low', 
      description: 'Detects only significant movement. Fewer false alarms.' 
    },
    { 
      value: 'medium', 
      label: 'Medium', 
      description: 'Balanced detection. Recommended for most homes.' 
    },
    { 
      value: 'high', 
      label: 'High', 
      description: 'Detects subtle movements. May increase false alarms.' 
    },
  ];

  const handleSensitivityChange = (level: SensitivityLevel) => {
    setSensitivity(level);
    if (smartMode) {
      Alert.alert(
        'Smart Mode Active',
        'Sensitivity will be automatically adjusted based on your patterns.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleSmartModeToggle = (value: boolean) => {
    setSmartMode(value);
    if (value) {
      Alert.alert(
        'Smart Mode Enabled',
        'HomeSense will learn your daily patterns and automatically adjust sensitivity to reduce false alarms.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleQuietHoursToggle = (value: boolean) => {
    setQuietHours(prev => ({ ...prev, enabled: value }));
  };

  const handleTimeChange = (type: 'start' | 'end') => {
    Alert.alert(
      'Set Time',
      `This would open a time picker for ${type} time.`,
      [{ text: 'OK' }]
    );
  };

  const renderSensitivityOption = (option: typeof sensitivityLevels[0]) => (
    <TouchableOpacity
      key={option.value}
      style={[
        styles.sensitivityOption,
        sensitivity === option.value && styles.sensitivityOptionActive
      ]}
      onPress={() => handleSensitivityChange(option.value)}
    >
      <View style={styles.optionHeader}>
        <Text style={[
          styles.optionLabel,
          sensitivity === option.value && styles.optionLabelActive
        ]}>
          {option.label}
        </Text>
        <View style={[
          styles.radioButton,
          sensitivity === option.value && styles.radioButtonActive
        ]}>
          {sensitivity === option.value && (
            <View style={styles.radioButtonInner} />
          )}
        </View>
      </View>
      <Text style={[
        styles.optionDescription,
        sensitivity === option.value && styles.optionDescriptionActive
      ]}>
        {option.description}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {/* Sensitivity Level */}
        <Card style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Sensitivity Level</Text>
            <TouchableOpacity onPress={() => Alert.alert('Info', 'Adjust how sensitive motion detection should be.')}>
              <Ionicons name="information-circle-outline" size={20} color={Colors.accent} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.sensitivityOptions}>
            {sensitivityLevels.map(renderSensitivityOption)}
          </View>
        </Card>

        {/* Smart Mode */}
        <Card style={styles.section}>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Smart Mode</Text>
              <Text style={styles.settingDescription}>
                AI learns your patterns and auto-adjusts sensitivity
              </Text>
            </View>
            <Switch
              value={smartMode}
              onValueChange={handleSmartModeToggle}
              trackColor={{ false: Colors.gray300, true: Colors.black }}
              thumbColor={smartMode ? Colors.white : Colors.gray400}
            />
          </View>
        </Card>

        {/* Custom Sensitivity (only when Smart Mode is off) */}
        {!smartMode && (
          <Card style={styles.section}>
            <Text style={styles.sectionTitle}>Custom Sensitivity</Text>
            <View style={styles.sliderContainer}>
              <Text style={styles.sliderLabel}>Low</Text>
              <Slider
                style={styles.slider}
                minimumValue={0}
                maximumValue={100}
                value={customValue}
                onValueChange={setCustomValue}
                minimumTrackTintColor={Colors.black}
                maximumTrackTintColor={Colors.gray300}
                thumbStyle={{ backgroundColor: Colors.black }}
              />
              <Text style={styles.sliderLabel}>High</Text>
            </View>
            <Text style={styles.sliderValue}>{Math.round(customValue)}%</Text>
          </Card>
        )}

        {/* Quiet Hours */}
        <Card style={styles.section}>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Quiet Hours</Text>
              <Text style={styles.settingDescription}>
                Reduce sensitivity during specified hours
              </Text>
            </View>
            <Switch
              value={quietHours.enabled}
              onValueChange={handleQuietHoursToggle}
              trackColor={{ false: Colors.gray300, true: Colors.black }}
              thumbColor={quietHours.enabled ? Colors.white : Colors.gray400}
            />
          </View>

          {quietHours.enabled && (
            <View style={styles.timeSettings}>
              <TouchableOpacity 
                style={styles.timeButton}
                onPress={() => handleTimeChange('start')}
              >
                <Text style={styles.timeLabel}>Start Time</Text>
                <Text style={styles.timeValue}>{quietHours.startTime}</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.timeButton}
                onPress={() => handleTimeChange('end')}
              >
                <Text style={styles.timeLabel}>End Time</Text>
                <Text style={styles.timeValue}>{quietHours.endTime}</Text>
              </TouchableOpacity>
            </View>
          )}
        </Card>

        {/* Tips */}
        <Card style={[styles.section, styles.tipsSection]}>
          <View style={styles.tipsHeader}>
            <Ionicons name="bulb-outline" size={24} color={Colors.accent} />
            <Text style={styles.tipsTitle}>Tips for Better Detection</Text>
          </View>
          
          <View style={styles.tipsList}>
            <Text style={styles.tipItem}>• Place devices 6-8 feet high for optimal coverage</Text>
            <Text style={styles.tipItem}>• Avoid pointing sensors at windows or heat sources</Text>
            <Text style={styles.tipItem}>• Enable Smart Mode for automatic optimization</Text>
            <Text style={styles.tipItem}>• Use Quiet Hours to reduce nighttime alerts</Text>
          </View>
        </Card>
      </View>
    </SafeAreaView>
  );
};
